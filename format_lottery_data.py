import re
import os

def format_lottery_data(input_file_path, output_file_path):
    """
    读取原始彩票数据文件，将其转换为指定的格式，并保存到新文件。
    原始格式: YYYY-MM-DD\t R1 R2 R3 R4 R5 R6 \t B (或类似空格/制表符分隔)
    目标格式: YYYY-MM-DD,"[R1,R2,R3,R4,R5,R6]",B

    参数:
        input_file_path (str): 输入文件的路径。
        output_file_path (str): 输出文件的路径。
    """
    formatted_lines = []
    error_messages = [] # 用于收集所有错误/警告信息
    lines_processed = 0
    lines_skipped = 0

    print(f"开始处理输入文件: {input_file_path}")

    if not os.path.exists(input_file_path):
        msg = f"错误: 输入文件未找到于 {input_file_path}"
        print(msg)
        error_messages.append(msg)
        try:
            with open(output_file_path, 'w', encoding='utf-8') as outfile:
                for err_msg in error_messages:
                    outfile.write(err_msg + '\n')
            print(f"错误信息已写入到: {output_file_path}")
        except Exception as e_write:
            print(f"写入错误信息到输出文件失败: {e_write}")
        return

    if not os.path.isfile(input_file_path):
        msg = f"错误: 输入路径不是一个文件 {input_file_path}"
        print(msg)
        error_messages.append(msg)
        try:
            with open(output_file_path, 'w', encoding='utf-8') as outfile:
                for err_msg in error_messages:
                    outfile.write(err_msg + '\n')
            print(f"错误信息已写入到: {output_file_path}")
        except Exception as e_write:
            print(f"写入错误信息到输出文件失败: {e_write}")
        return

    try:
        with open(input_file_path, 'r', encoding='utf-8') as infile:
            for line_number, line in enumerate(infile, 1):
                lines_processed += 1
                line = line.strip()
                if not line:
                    msg = f"信息 (行 {line_number}): 空行，已跳过。"
                    error_messages.append(msg)
                    lines_skipped +=1
                    continue
                
                parts = re.split(r'\s+', line) 
                
                if len(parts) == 8:
                    date_str = parts[0]
                    red_balls_str = parts[1:7]
                    blue_ball_str = parts[7]
                    
                    valid_line = True
                    if not re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
                        msg = f"警告 (行 {line_number}): 日期格式不正确 '{date_str}'. 行内容: {line}"
                        print(msg)
                        error_messages.append(msg)
                        valid_line = False
                    
                    for i, ball in enumerate(red_balls_str):
                        if not ball.isdigit():
                            msg = f"警告 (行 {line_number}): 红球 {i+1} ('{ball}') 不是数字. 行内容: {line}"
                            print(msg)
                            error_messages.append(msg)
                            valid_line = False
                            break
                    if valid_line and not blue_ball_str.isdigit():
                        msg = f"警告 (行 {line_number}): 蓝球 ('{blue_ball_str}') 不是数字. 行内容: {line}"
                        print(msg)
                        error_messages.append(msg)
                        valid_line = False

                    if valid_line:
                        formatted_red_balls = f'"[{