import numpy as np
from scipy.stats import norm

class BayesianEstimator:
    """
    一个简单的贝叶斯估计算法，用于根据一系列观测数据预测下一个值。
    假设数据服从高斯分布，并且我们对均值有一个高斯先验。
    方差被认为是已知的。
    """

    def __init__(self, prior_mean, prior_variance, data_variance):
        """
        初始化贝叶斯估计器。

        参数:
            prior_mean (float): 均值的先验分布的均值。
            prior_variance (float): 均值的先验分布的方差。
            data_variance (float): 数据的方差（假设已知且恒定）。
        """
        self.prior_mean = prior_mean
        self.prior_variance = prior_variance
        self.data_variance = data_variance
        self.posterior_mean = prior_mean
        self.posterior_variance = prior_variance
        self.data_points = []

    def update(self, new_data_point):
        """
        使用新的数据点更新后验分布。

        参数:
            new_data_point (float): 新的观测数据点。
        """
        self.data_points.append(new_data_point)
        n = len(self.data_points)

        # 如果只有一个数据点，直接使用先验和这个数据点更新
        if n == 1:
            # 根据贝叶斯公式更新后验均值和方差
            # 后验方差 = 1 / (1/先验方差 + 1/数据方差)
            self.posterior_variance = 1 / (1 / self.prior_variance + 1 / self.data_variance)
            # 后验均值 = 后验方差 * (先验均值/先验方差 + 新数据点/数据方差)
            self.posterior_mean = self.posterior_variance * (
                self.prior_mean / self.prior_variance + new_data_point / self.data_variance
            )
        else:
            # 如果有多个数据点，使用所有数据点的均值来更新
            # 这里的简化处理是每次都基于初始先验和所有累积数据进行更新
            # 更精确的方法是序贯更新，但为了简单起见，我们重新计算
            sum_x = sum(self.data_points)
            
            # 后验方差 = 1 / (1/先验方差 + n/数据方差)
            self.posterior_variance = 1 / (1 / self.prior_variance + n / self.data_variance)
            # 后验均值 = 后验方差 * (先验均值/先验方差 + 数据总和/数据方差)
            self.posterior_mean = self.posterior_variance * (
                self.prior_mean / self.prior_variance + sum_x / self.data_variance
            )

    def predict(self):
        """
        预测下一个数据点的值。
        预测值是后验均值。

        返回:
            float: 预测的下一个数据点的值。
        """
        return self.posterior_mean

    def get_posterior_distribution(self):
        """
        获取当前后验分布的参数（均值和方差）。

        返回:
            tuple: (后验均值, 后验方差)
        """
        return self.posterior_mean, self.posterior_variance

# 示例用法
if __name__ == "__main__":
    # 假设我们对数据的均值有一个先验信念：均值为50，方差为100 (标准差为10)
    # 假设我们知道数据的真实方差是 25 (标准差为5)
    prior_mu = 50.0
    prior_var = 100.0 
    data_var = 25.0

    estimator = BayesianEstimator(prior_mean=prior_mu, prior_variance=prior_var, data_variance=data_var)

    print(f"初始先验均值: {estimator.prior_mean:.2f}, 初始先验方差: {estimator.prior_variance:.2f}")
    print(f"预测下一个值 (基于先验): {estimator.predict():.2f}\n")

    # 观测数据
    observed_data = [55, 60, 52, 65, 58]

    for i, data_point in enumerate(observed_data):
        estimator.update(data_point)
        current_mean, current_var = estimator.get_posterior_distribution()
        prediction = estimator.predict()
        print(f"观测到数据点 {i+1}: {data_point}")
        print(f"更新后验均值: {current_mean:.2f}, 更新后验方差: {current_var:.2f}")
        print(f"预测下一个值: {prediction:.2f}\n")

    # 最终预测
    final_prediction = estimator.predict()
    print(f"所有数据处理完毕后，最终预测下一个值为: {final_prediction:.2f}")

    # 我们可以看到，随着数据的增多，后验方差逐渐减小，表明我们对均值的估计越来越确定。
    # 预测值也逐渐向观测数据的均值靠拢。