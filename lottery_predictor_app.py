import streamlit as st
import pandas as pd
import numpy as np
from collections import Counter

# 蓝球号码范围
BLUE_BALL_MIN = 1
BLUE_BALL_MAX = 33 # 已将最大值从16修改为33
ALL_BLUE_BALLS = list(range(BLUE_BALL_MIN, BLUE_BALL_MAX + 1))

def predict_next_blue_ball(history_data):
    """
    根据历史蓝球数据预测下一个蓝球号码。
    使用频率统计方法，并加入拉普拉斯平滑 (每个号码初始计数为1)。

    参数:
        history_data (list of int): 历史蓝球号码列表。

    返回:
        int: 预测的下一个蓝球号码 (出现频率最高的)。
        dict: 每个蓝球号码的预测概率。
    """
    if not history_data:
        # 如果没有历史数据，随机选择一个或返回一个默认值/概率
        # 这里我们返回概率均等，并随机选择一个
        probabilities = {ball: 1/len(ALL_BLUE_BALLS) for ball in ALL_BLUE_BALLS}
        predicted_ball = np.random.choice(ALL_BLUE_BALLS)
        return predicted_ball, probabilities

    # 拉普拉斯平滑：给每个可能的蓝球一个初始计数1
    counts = Counter(ALL_BLUE_BALLS) # 每个球计数为1
    counts.update(history_data)      # 更新实际观测到的计数

    total_counts = sum(counts.values())

    probabilities = {ball: count / total_counts for ball, count in counts.items()}

    # 选择概率最高的球作为预测结果
    # 如果有多个概率相同的最高值，随机选择一个
    max_prob = 0
    # 找出最大概率，因为Counter不能直接用max()取value最大的key
    for ball in probabilities:
        if probabilities[ball] > max_prob:
            max_prob = probabilities[ball]
    
    predicted_balls = [ball for ball, prob in probabilities.items() if prob == max_prob]
    
    if not predicted_balls: #理论上不会发生，因为probabilities不会为空
        predicted_ball = np.random.choice(ALL_BLUE_BALLS)
    else:
        predicted_ball = np.random.choice(predicted_balls) # 从最高概率的球中随机选一个

    return predicted_ball, probabilities

def main():
    """ Streamlit 应用主函数 """
    st.set_page_config(page_title="双色球蓝球预测器", layout="wide")
    st.title("双色球蓝球预测器")

    st.sidebar.header("说明")
    st.sidebar.info(
        "本工具根据历史蓝球数据，使用简单的频率统计方法（加入拉普拉斯平滑）来预测下一个蓝球号码。"
        "蓝球范围为1-33。"
        "您可以上传历史数据文件（CSV或TXT，每行一个数字），或手动输入数据。"
    )

    # 初始化 session_state 用于存储历史数据
    if 'history_blue_balls' not in st.session_state:
        st.session_state.history_blue_balls = []

    # --- 数据输入部分 ---
    st.header("数据输入")
    input_col1, input_col2 = st.columns(2)

    with input_col1:
        st.subheader("上传历史数据文件")
        uploaded_file = st.file_uploader("选择一个 CSV 或 TXT 文件 (每行一个蓝球号码)", type=["csv", "txt"])
        if uploaded_file is not None:
            try:
                if uploaded_file.name.endswith('.csv'):
                    df = pd.read_csv(uploaded_file, header=None)
                else: # txt file
                    df = pd.read_csv(uploaded_file, header=None, delim_whitespace=True)
                
                new_data = df[0].astype(int).tolist()
                # 数据校验
                valid_new_data = [x for x in new_data if BLUE_BALL_MIN <= x <= BLUE_BALL_MAX]
                invalid_new_data_count = len(new_data) - len(valid_new_data)
                
                st.session_state.history_blue_balls.extend(valid_new_data)
                st.session_state.history_blue_balls = sorted(list(set(st.session_state.history_blue_balls))) # 去重并排序
                st.success(f"成功加载 {len(valid_new_data)} 个有效蓝球号码！")
                if invalid_new_data_count > 0:
                    st.warning(f"忽略了 {invalid_new_data_count} 个无效数据（不在1-33范围内）。")
            except Exception as e:
                st.error(f"文件读取或处理失败: {e}")

    with input_col2:
        st.subheader("手动添加历史蓝球")
        manual_input_str = st.text_input("输入蓝球号码 (用空格或逗号分隔, 例如: 5 10 12)")
        if st.button("添加手动输入的数据"):
            if manual_input_str:
                try:
                    # 替换逗号为空格，然后按空格分割
                    parts = manual_input_str.replace(',', ' ').split()
                    new_manual_data = [int(p.strip()) for p in parts if p.strip().isdigit()]
                    
                    valid_manual_data = [x for x in new_manual_data if BLUE_BALL_MIN <= x <= BLUE_BALL_MAX]
                    invalid_manual_data_count = len(new_manual_data) - len(valid_manual_data)

                    st.session_state.history_blue_balls.extend(valid_manual_data)
                    st.session_state.history_blue_balls = sorted(list(set(st.session_state.history_blue_balls))) # 去重并排序
                    st.success(f"成功添加 {len(valid_manual_data)} 个手动输入的有效蓝球号码！")
                    if invalid_manual_data_count > 0:
                        st.warning(f"忽略了 {invalid_manual_data_count} 个无效数据。")
                    manual_input_str = "" # 清空输入框
                except ValueError:
                    st.error("输入格式错误，请输入数字，并用空格或逗号分隔。")
            else:
                st.warning("请输入蓝球号码。")

    # --- 显示历史数据 ---
    st.header("当前历史蓝球数据")
    if st.session_state.history_blue_balls:
        st.write(f"总共 {len(st.session_state.history_blue_balls)} 期历史数据 (已去重并排序):")
        # 每行显示10个数据
        display_data_rows = [st.session_state.history_blue_balls[i:i + 10] for i in range(0, len(st.session_state.history_blue_balls), 10)]
        for row in display_data_rows:
            st.text(', '.join(map(str, row)))
        
        if st.button("清空所有历史数据"):
            st.session_state.history_blue_balls = []
            st.experimental_rerun()
    else:
        st.info("暂无历史数据。请上传文件或手动输入。")

    # --- 预测部分 ---
    st.header("预测下一个蓝球号码")
    if st.button("开始预测"):
        if st.session_state.history_blue_balls:
            predicted_ball, probabilities = predict_next_blue_ball(st.session_state.history_blue_balls)
            st.subheader(f"预测的下一个蓝球号码是: {predicted_ball}")
            
            st.subheader("各蓝球号码出现概率:")
            prob_df = pd.DataFrame(list(probabilities.items()), columns=["蓝球号码", "预测概率"])
            prob_df = prob_df.sort_values(by="预测概率", ascending=False).reset_index(drop=True)
            st.dataframe(prob_df.style.format({"预测概率": "{:.2%}"}))
            
            # 可视化概率
            st.bar_chart(prob_df.set_index("蓝球号码")["预测概率"])
        else:
            st.warning("请输入或上传历史数据后才能进行预测。")

    st.sidebar.markdown("---")
    st.sidebar.markdown("**注意:** 彩票具有随机性，本工具仅供娱乐和参考，不构成任何投资建议。")

if __name__ == "__main__":
    # 提示用户如何运行，以及可能需要的依赖
    print("要运行此Streamlit应用，请在终端中执行以下命令:")
    print("streamlit run lottery_predictor_app.py")
    print("\n如果您尚未安装streamlit或pandas，请先安装:")
    print("pip install streamlit pandas numpy")
    main()